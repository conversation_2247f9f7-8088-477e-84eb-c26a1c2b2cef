@import url('https://fonts.cdnfonts.com/css/dejavu-serif');

#pistn-s2w {
  position: fixed;
  top: 10% !important;
  left: 0;
  height: 80%;
  z-index: 1000000;
  display: none;
  border: 3px solid #e6b672;
  border-width: 3px 3px 3px 0;
  border-radius: 5px;
  box-shadow: 0 0 10px #333;
  filter: brightness(0.9);
  transition: left 0.3s ease-in 0s;
  font-family: helvetica, arial, sans-serif;
  background-color: #004069;
  line-height: 1.3;
}
#pistn-s2w:hover {
  filter: brightness(1);
}

#pistn-s2w-mobile-recaptcha {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9999999;
  padding: 20px;
  background: lightslategray;
  opacity: 0.95;
}

#pistn-s2w-bg-img {
  height: 100%;
  position: relative;
}
#pistn-s2w-bg {
  height: 100%;
  right: 0;
  position: absolute;
  z-index: -10;
  overflow: hidden;
}

#pistn-s2w-wheel {
  position: absolute;
  display: block;
  right: 12.5%;
  top: 17%;
  z-index: -20;
  filter: brightness(0.9);
  transition: filter 0.5s ease-in 0s;
  height: 62%;
  cursor: pointer;
}

#pistn-s2w-handle {
  top: 43%;
  right: -89px;
  transform: rotate(90deg);
  position: absolute;
  font-size: 22px;
  font-weight: bold;
  background: linear-gradient(#a4923e 0%, #fecc84 40%, #caaf80 60%, #fecc84 70%, #a4923e 100%);
  padding: 3px 8px;
  border-radius: 8px 8px 0 0;
  color: #121a20;
  text-shadow: 1px 1px #9ebbce;
  cursor: pointer;
  border-width: 1px 1px 0px 1px;
  border-style: solid;
  border-color: #455f73;
  box-shadow: 0 0 10px #333;
}

#pistn-s2w-text {
  position: absolute;
  top: 8%;
  left: 5%;
  padding: 8px 15px;
  scrollbar-color: white rgba(217, 214, 212, 0.55);
  width: 55%;
}

#pistn-s2w-h1 {
  color: #e0e8ff;
  text-shadow: 2px 2px 2px #3b88ff;
  text-transform: uppercase;
  font-size: 38px;
  margin-top: 15px;
}

#pistn-s2w-intro p {
  font-size: 18px;
}

#pistn-s2w-intro {
  color: #e0e8ff;
}

#pistn-s2w-intro form p {
  clear: left;
  margin-bottom: 6px;
  overflow: hidden;
}

#pistn-s2w-intro strong {
  color: #ff8187;
  color: #ed71ff;
}

#pistn-s2w-spin_to_win-accept a {
  color: #ed71ff;
}

#pistn-s2w-intro-p1 {
  width: 500px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.pistn-s2w-button {
  display: block;
  margin-top: 30px;
  margin-top: 0px;
  font-size: 30px;
  border-radius: 10px;
  box-shadow: 0px 0px 15px rgb(215, 157, 157);
  padding: 10px 40px;
  font-family: Georgia, serif !important;
  background: #881218;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-align: center;
}

#pistn-s2w-spin-button {
  /* float: right; */
  top: -7px;
  padding-left: 45px;
  width: 240px;
  font-size: 27px;
}
#pistn-s2w-register-button {
  padding: 8px 10px 10px;
  clear: left;
  display: inline-block;
  margin-top: 0px;
  font-size: 20px;
}
.pistn-s2w-button:hover {
  box-shadow: 0px 0px 15px #eed5d5;
  background: #972026;
}

#pistn-s2w-practice {
  position: absolute;
  inset: 0 auto auto 0;
  background: #27ccff;
  transform-origin: 100% 0;
  transform: translate(-29%) rotate(-45deg);
  box-shadow: 0 0 0 999px #27ccff;
  clip-path: inset(0 -100%);
  font-size: 16px;
  color: black;
  white-space: nowrap;
  overflow: hidden;
}

#pistn-s2w-overlay {
  background: #46b9a8;
  opacity: 0.4;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  display: block;
  border-radius: 20px;
}

#pistn-s2w-h1-arrow {
  display: none;
  color: #fdb8bc;
}

#pistn-s2w-collapse u, #pistn-s2w-up-arrow, #pistn-s2w-bg {
  pointer-events: none;
}
#pistn-s2w-collapse {
  display: none;
  color: #ffc1c5;
  padding-left: 10px;
  cursor: pointer;
  text-decoration: none;
}
#pistn-s2w-up-arrow {
  position: relative;
  top: -2px;
  font-size: larger;
  text-decoration: none;
}

#pistn-s2w-close-handle {
  position: absolute;
  top: -10px;
  background: rgba(227, 204, 149, 0.62);
  border-radius: 18px;
  padding: 0px 5px 2px 4px;
  font-weight: bold;
  font-family: Tahoma, sans-serif !important;
  color: #121a20;
  text-shadow: 1px 0px #121a20;
  right: -12px;
  font-size: 17px;
  cursor: pointer;
}

#pistn-s2w label {
  display: inline-block;
  float: left;
  font-weight: bold;
  color: #fdb8bc;
  color: #f194ff;
  position: relative;
  top: 8px;
  padding-right: 15px;
  width: 110px;
  font-size: 16px;
  text-align: right;
  clear: left;
}

#pistn-s2w input[type="text"] {
  width: 250px;
  float: left;
  padding: 6px 10px;
  border-radius: 3px;
  border: 1px solid #4a6073;
  font-size: 18px;
  color: #222;
  margin-bottom: 4px;
}

#pistn-s2w-practice-wheel {
  background: #7ce0ff;
  opacity: 0.7;
  font-size: 24px;
  color: #222;
  padding: 6px 8px;
  font-weight: bold;
  display: block;
  position: absolute;
  top: 42.5%;
  left: 35%;
  overflow: hidden;
  white-space: nowrap;
  text-align: center;
  border-radius: 10px;
  text-decoration: none !important;
  cursor: pointer;
}

#pistn-s2w-registered {
  background: #fbeec9;
  display: inline-block;
  width: 100px;
  color: #222;
  padding: 5px 10px;
  text-align: center;
  position: relative;
  margin-top: 10px;
}

#pistn-s2w-email-phone-p {
  padding-top: 10px;
}
#pistn-s2w-text.mobile #pistn-s2w-email-phone-p label {
  top: 6px !important;
}
#pistn-s2w-intro p input[type="text"]:last-child {
  position: relative;
  top: 8px;
}

#pistn-s2w #pistn-s2w-last_name_label, #pistn-s2w #pistn-s2w-phone_label {
  clear: left;
  position: relative;
  top: 13px;
}

#pistn-s2w #recaptcha-box {
  clear: left;
  padding-top: 0px;
  margin-bottom: 5px;
}

#pistn-s2w #reload-recaptcha {
  text-decoration: underline;
  cursor: pointer;
}

#pistn-s2w #pistn-s2w-texting-accept {
  padding-top: 10px;
  padding-bottom: 0;
  margin-bottom: 0;
  font-size: 12px;
}

#pistn-s2w #pistn-s2w-texting-accept a {
  color: #fdb8bc;
}

/* fixed-right */

#pistn-s2w.fixed-right {
  left: unset;
  right: 0;
  border-width: 3px 0px 3px 3px;
  transition: right 0.3s ease-in 0s;
}
#pistn-s2w.fixed-right #pistn-s2w-bg {
  right: unset;
  left: 0;
  transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
}
#pistn-s2w.fixed-right #pistn-s2w-handle {
  left: -89px;
  right: unset;
  transform: rotate(270deg);
}
#pistn-s2w.fixed-right #pistn-s2w-wheel {
  right: unset;
  left: 12.5%;
}
#pistn-s2w.fixed-right #pistn-s2w-text {
  left: unset;
  right: 5%;
}
#pistn-s2w.fixed-right #pistn-s2w-text.mobile {
  margin-right: -43%;
  margin-left: unset;
}


/* Mobile */
#pistn-s2w-text.mobile #pistn-s2w-collapse {
  display: inline-block;
  position: relative;
  top: -17px;
  padding-left: 10px;
  margin-top: 10px;
  font-size: 14px;
}
#pistn-s2w-text.mobile {
  left: 50%;
  margin-left: -38%;
  width: 70%;
  top: 2%;
  overflow-x: hidden;
  box-sizing: border-box;
}
#pistn-s2w-text.mobile #pistn-s2w-email-phone-p {
  padding-top: 10px;
}
#pistn-s2w-text.mobile #pistn-s2w-last_name_label, #pistn-s2w-text.mobile #pistn-s2w-phone_label {
  clear: none;
  top: 6px;
}
#pistn-s2w-text.mobile input[type="text"]:last-child {
  top: 0px;
}
#pistn-s2w-text.mobile #pistn-s2w-h1 {
  margin-top: 10px;
  margin-bottom: 10px;
}
#pistn-s2w-text.mobile #pistn-s2w-spin-button {
  margin-top: 20px;
  margin-top: 0px;
}
#pistn-s2w-text.mobile #pistn-s2w-register-button {
  /*
  clear: none;
  position: relative;
  top: -12px;
  margin-top: 0;
  margin-top: 15px;
  */
}
#pistn-s2w-text.mobile #pistn-s2w-h1-arrow {
  display: inline;
  position: relative;
  font-size: 26px;
  top: -3px;
  cursor: pointer;
  margin-bottom: 5px;
}
#pistn-s2w-text.mobile #pistn-s2w-overlay {
  background: #279585;
  opacity: 0.9;
}
#pistn-s2w-text.mobile #pistn-s2w-registered {
  top: 0px;
  margin-top: 10px;
}
#pistn-s2w #pistn-s2w-text.mobile input[type="text"] {
  width: 135px;
}
#pistn-s2w #pistn-s2w-register-form label.error {
  display: none !important;
}
#pistn-s2w input[type="checkbox"] {
  float: left;
  margin-right: 10px;
  margin-top: 5px;
  margin-left: 0px;
  clear: left;
  display: block !important;
  width: auto !important;
  height: auto !important;
}

#pistn-s2w-spin_to_win-accept, #pistn-s2w-texting-accept {
  clear: left;
  margin-left: 125px;
  padding-left: 25px;
  text-indent: -25px;
  font-size: 12px;
  line-height: 1.3;
  width: 12rem;
  box-sizing: border-box;
}

#pistn-s2w-register-form input[type="checkbox"].error {
  outline: 1px solid red;
}
#pistn-s2w-mobile-clear-left {
  clear: left !important;
}

#pistn-s2w-mystery-chances {
  color: black;
  width: 380px;
  background: #e0e8ff;
  padding: 5px 15px 8px;
  margin-top: 5px;
}

.pistn-s2w-mystery-prize:first-child {
  display: block;
}
.pistn-s2w-mystery-prize {
  display: none;
  text-align: center;
}
.pistn-s2w-mystery-prize-title {
  display: block;
  background: black;
  color: yellow;
  padding: 5px;
  margin: 5px;
  font-family: 'DejaVu Serif', sans-serif;
  font-weight: bold;
  text-shadow: 2px 2px #756301;
}
.pistn-s2w-mystery-prize-chance {
  display: block;
  font-size: 13px;
}
.pistn-s2w-mystery-prizes {
  font-size: 14px;
}
#pistn-s2w-mystery-supplies {
  font-size: 11px;
}

#pistn-s2w-modal {
  width: 330px;
  height: 140px;
  z-index: 99999999;
  background: #444;
  color: white;
  position: fixed;
  padding: 20px;
  top: 50%;
  left: 50%;
  margin-left: -150px;
  margin-top: -50px;
  border: 3px ridge #888;
  display: none;
}
#pistn-s2w-modal-bg {
  background: #aaa;
  position: fixed;
  height: 100%;
  width: 100%;
  z-index: 9999999;
  top: 0;
  left: 0;
  opacity: 0.6;
  display: none;
}

#pistn-s2w-modal-options {
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
}

.pistn-s2w-close-option {
  display: block;
  background: #eee;
  border: 1px solid #aaa;
  border-radius: 4px;
  padding: 3px 5px;
  color: #222 !important;
  cursor: pointer;
  font-weight: normal !important;
}
.pistn-s2w-close-option:last-child {
  background: cyan;
  font-weight: bold !important;
  border-color: #00a4a4;
}

#pistn-s2w-close-span {
  font-family: helvetica, arial, sans-serif;
  color: #ffc1c5;
  text-transform: none;
  font-size: 16px;
  font-weight: normal;
  margin-left: 8px;
  cursor: pointer;
  position: relative;
  text-decoration: underline;
  top: -12px;
}
#pistn-s2w-close-span u {
  pointer-events: none;
}

#pistn-s2w-close-success {
  display: none;
  position: absolute;
  color: #ffc1c5;
  top: 3%;
  text-decoration: underline;
  cursor: pointer;
  text-align: center;
  width: 45px;
  font-weight: normal;
}

#pistn-s2w-zip-p label {
  top: 15px;
}
#pistn-s2w-text.mobile #pistn-s2w-zip-p label {
  top: 6px
}

@media only screen and (min-width: 450px) {
  #pistn-s2w-spin_to_win-accept {
    padding-top: 20px !important;
  }
  #pistn-s2w-register-p {
    margin-top: 15px;
  }
  #pistn-s2w-spin_to_win-accept {
    margin-bottom: 10px;
  }
  #recaptcha-box {
  }
}

@media only screen and (max-width: 450px) {
  #pistn-s2w {
    top: 2% !important;
    height: 96% !important;
  }

  #pistn-s2w-spin-button {
    padding: 6px 20px !important;
    font-size: 22px !important;
    margin-top: 5px !important;
  }
  #pistn-s2w-practice {
    font-size: 13px !important;
  }
  #pistn-s2w-h1 {
    margin-top: 3px !important;
    margin-bottom: 8px !important;
    font-size: 28px !important;
  }
  #pistn-s2w-text {
    padding: 8px !important;
    padding-bottom: 0 !important;
  }
  #pistn-s2w-text.mobile {
    overflow-x: hidden;
    overflow-y: auto;
    height: 94% !important;
  }

  #pistn-s2w-intro {
    overflow-x: hidden;
    padding-bottom: 10px;
  }

  #pistn-s2w-mystery-chances {
    margin-top: 5px !important;
    margin-bottom: 8px !important;
  }
  #pistn-s2w-intro-p1 {
    margin-bottom: 5px !important;
  }
  #pistn-s2w-instructions {
    margin-bottom: 3px !important;
    font-size: 14px !important;
  }

  /* Compact form layout */
  #pistn-s2w-register-form p {
    margin-bottom: 5px !important;
  }

  #pistn-s2w-email-phone-p {
    padding-top: 3px !important;
  }

  #pistn-s2w-zip-p {
    padding-top: 3px !important;
  }

  #pistn-s2w-phone-p {
    padding-top: 3px !important;
  }

  #pistn-s2w-spin_to_win-accept {
    margin-bottom: 5px !important;
    font-size: 13px !important;
    line-height: 1.2 !important;
    padding-top: 5px !important;
  }

  #pistn-s2w-register-p {
    margin-bottom: 5px !important;
  }

  #pistn-s2w-register-button {
    padding: 6px 10px !important;
    font-size: 18px !important;
  }

  #customer_full_name, #customer_email, #customer_zip_code, #customer_mobile_phone_numbers {
    margin-right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    padding: 6px !important;
    font-size: 14px !important;
  }

  #pistn-s2w input[type="text"] {
    width: 100% !important;
    max-width: 100% !important;
    font-size: 14px !important;
    padding: 6px !important;
  }

  /* Compact labels */
  #pistn-s2w label {
    font-size: 14px !important;
    margin-bottom: 2px !important;
  }
}

@media only screen and (max-width: 370px) {
  #pistn-s2w-h1 {
    font-size: 24px !important;
    margin-bottom: 5px !important;
  }

  /* Extra small screens - more compact */
  #pistn-s2w-text.mobile {
    width: 92% !important;
    margin-right: -46% !important;
    padding: 5px !important;
  }

  #pistn-s2w-spin_to_win-accept {
    font-size: 12px !important;
    line-height: 1.1 !important;
    margin-bottom: 3px !important;
  }

  #pistn-s2w-register-button {
    font-size: 16px !important;
    padding: 5px 8px !important;
  }

  #pistn-s2w-spin-button {
    font-size: 20px !important;
    padding: 5px 15px !important;
  }

  #pistn-s2w-register-form p {
    margin-bottom: 3px !important;
  }

  #customer_full_name, #customer_email, #customer_zip_code, #customer_mobile_phone_numbers {
    padding: 4px !important;
    font-size: 13px !important;
  }

  #pistn-s2w label {
    font-size: 13px !important;
  }
}

/* Desktop and tablet - wider form layout */
@media only screen and (min-width: 769px) {
  #pistn-s2w-text {
    width: 60% !important;
  }

  #pistn-s2w-intro-p1 {
    width: 550px !important;
  }

  #pistn-s2w input[type="text"] {
    width: 280px !important;
  }

  #pistn-s2w label {
    width: 120px !important;
    padding-right: 20px !important;
  }

  #pistn-s2w-mystery-chances {
    width: 420px !important;
  }
}

/* Additional mobile improvements for all small screens */
@media only screen and (max-width: 600px) {
  #pistn-s2w-text.mobile * {
    box-sizing: border-box;
  }

  #pistn-s2w-register-form {
    overflow-x: hidden;
  }

  #pistn-s2w-spin_to_win-accept {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  #pistn-s2w-spin_to_win-accept a {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

#pistn-s2w-phone-p label {
  top: 14px;
}



/*  OVERRIDES FOR SPIN TO WIN */
#pistn-s2w-handle, #pistn-s2w-handle:hover {
  text-shadow: none !important;
  background:	#87ceeb !important;
}

#pistn-s2w-close-handle {
  background:	#87ceeb !important;
}

#pistn-s2w {
  border: 3px solid	#87ceeb !important;
}

#pistn-s2w-overlay {
  background: #00304b !important;
}

#pistn-s2w-h1 {
  color: #e0e8ff;
  text-shadow: none !important;
}

#pistn-s2w-h1-arrow {
  display: none;
  color:	#87ceeb !important;
}

#pistn-s2w-close-span {
  color:	#87ceeb !important;
}

#pistn-s2w-intro strong {
  color:	#87ceeb !important;
}

#pistn-s2w label {
  color:	#87ceeb !important;
}

#pistn-s2w-spin_to_win-accept a {
  color:	#87ceeb !important;
}

.pistn-s2w-button {
  box-shadow: none !important;
  background:	#87ceeb !important;
  color: black !important;
  font-weight: bold !important;
}

#pistn-s2w-mystery-chances {
  border-radius: 5px !important;
}

#pistn-s2w input[type="text"] {
  border-radius: 5px;
  border: 1px solid #4a6073;
  width: 12rem;
  font-size: 16px !important;
  max-width: 100%;
  box-sizing: border-box;
}

#pistn-s2w-zip-p {
  padding-top: 8px !important
}

#pistn-s2w-phone-p {
  padding-top: 8px !important
}


#pistn-s2w-spin-button, #pistn-s2w-mystery-chances {
  width: 12rem;
  max-width: 100%;
  box-sizing: border-box;
}

#pistn-s2w-register-button {
  width: 275px;
  max-width: 100%;
  box-sizing: border-box;
}
#pistn-s2w-text {
  top: 2% !important;
}

#pistn-s2w-collapse {
  color:	#87ceeb !important;
}

#pistn-s2w #pistn-s2w-text.mobile input[type="text"] {
  width: 10.5rem;
  font-size: 16px !important;
}

#rc-anchor-container {
  width: 260px !important;
}

#pistn-s2w-phone-p label {
  top: 14px;
}

#pistn-s2w-text.mobile {
  width: 95% !important;
  margin-right: -47.5% !important;
}