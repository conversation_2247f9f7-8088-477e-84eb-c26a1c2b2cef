class SpinToWinController < ApplicationController
  protect_from_forgery except: [:widget, :clear_spins]
  before_filter :set_cors_headers
  before_filter :protect_with_recaptcha, only: :register

  self.page_cache_directory = :domain_cache_directory
  caches_page :widget, :rules

  def widget
    require 'jsobfu'
    load_account
    # @account = Account.where(spin_to_win_key: params[:key]).first
    # raise ActiveRecord::RecordNotFound unless @account&.spin_to_win_key.present? && (@account.active_spin_wheel? || params[:preview_spin] == '1')

    # response.headers['Access-Control-Allow-Origin'] = '*'
    render layout: false
  end

  def register
    #  "customer" => {
    #                         "full_name" => "bob roberts",
    #                             "email" => "<EMAIL>",
    #              "mobile_phone_numbers" => "**********",
    #         "accepted_spin_to_win_text" => "1",
    #                          "zip_code" => "53711",
    #              "accepted_spin_to_win" => "1"
    #     },

    # we need to make sure that they have accepted the spin to win text and email
    # if @account.requires_spin_to_win_phone?
    #   accepted_spin_to_win_text = params[:customer][:accepted_spin_to_win_text] == '1'
    #   accepted_spin_to_win = params[:customer][:accepted_spin_to_win] == '1'
    #   @error = "You must accept the spin to win text and email" unless accepted_spin_to_win && accepted_spin_to_win_text if @account.requires_spin_to_win_phone?

    @account = Account.where(spin_to_win_key: params[:key]).first
    if @account
      if params[:customer][:mobile_phone_numbers].present?
        @customer = @account.customers.where(account_id: @account.id)
                            .select{ |c| c.numeric_phone_number == params[:customer][:mobile_phone_numbers].gsub(/[^\d]/, '') }.first
      end

      @customer ||= @account.customers.where(account_id: @account.id).where(email: params[:customer][:email]).first
      @customer ||= @account.customers.where(first_name: params[:customer][:full_name].split.first,
                                             last_name: params[:customer][:full_name].split.last,
                                             zip_code: params[:customer][:zip_code]).first

      @customer ||= Customer.new account_id: @account.id
      @customer.attributes = params.require(:customer).permit!
      @customer.account_id = @account.id
      generated_on = @customer.spin_prize_index_generated_on
      unless @customer.spin_prize_index.present? && generated_on.present? &&
             generated_on.beginning_of_month == Date.today.beginning_of_month
        @customer.spin_prize_index = (rand * 13).floor
        @customer.spin_prize_index_generated_on = Date.today
      end
      @customer.spin_to_win_reminder = true
      # create_customer_form_submission
      @customer.save!

      spin = @customer.wheel_spins.where("MONTH(spun_at) = ? AND YEAR(spun_at) = ?", Date.today.month, Date.today.year).first

      can_spin = !spin.present?
    else
      @error = "No account found"
    end

    respond_to do |format|
      if @error
        format.json { render json: { error: @error }, status: 422 }
      else
        format.json {
          render json: { notice: notice,
                         customer_id: @customer.id,
                         can_spin: can_spin,
                         spin_prize_index: @customer.spin_prize_index }
        }
      end
    end
  end

  def spin
    @account = Account.where(spin_to_win_key: params[:key]).first
    # raise ActiveRecord::RecordNotFound unless @account.spin_to_win_key.present? && (@account.active_spin_wheel? || params[:preview_spin] == '1')
    @customer = @account.customers.where(id: params[:customer_id]).first
    spin = @customer.wheel_spins.where("MONTH(spun_at) = ?", Date.today.month)
                    .where("YEAR(spun_at)  = ?", Date.today.year)
                    .first
    @can_spin = spin.blank?

    if @can_spin
      # index = (((params[:angle].to_f - 103.4) / -27.7) % 13).floor
      @spin_prize = @account.spin_prizes.where(position: @customer.spin_prize_index, mystery: false, month: Date.today.month).first
      if @spin_prize&.title == "Mystery Prize"
        @spin_prize = SpinPrize.choose_mystery_prize(@account)
      end
      if @spin_prize
        begin
          WheelSpin.transaction do
            @wheel_spin = WheelSpin.new customer_id: @customer.id,
                                        account_id: @account.id,
                                        first_name: @customer.first_name,
                                        last_name: @customer.last_name,
                                        spin_prize_id: @spin_prize.id,
                                        spin_prize_title: @spin_prize.title,
                                        spun_at: Time.now,
                                        zip_code: @customer.zip_code
            # res = TwilioSpinPrizeTexts.new(@wheel_spin).call
            @wheel_spin.save!
            @spin_prize.won_this_month += 1
            @spin_prize.save!
            SpinWheel.gen_spin_email_img(@account, @customer.name, @wheel_spin)
            @account.set_smtp
            PistnNotifier.spin_to_win(@customer, @wheel_spin).deliver_now
          end
        rescue StandardError => e
          @error = "Error sending prize"
          raise e
        end
      else
        @error = "No account found" unless @account.present?
        @error = "No customer found" unless @customer.present?
        @error = "Error retrieving prize"
      end
    else
      @error = "Error: Only one spin per month allowed"
    end

    respond_to do |format|
      if @error
        format.json { render json: { error: @error }, status: 422 }
      elsif @can_spin
        format.json {
          render json: {
            prize: @spin_prize.title,
            notice: "Please check your phone for your prize."
          }
        }
      else
        format.json { render json: { error: "The wheel can only be spun once a month." }, status: 422 }
      end
    end
  end

  def rules
    load_account
    render layout: false
  end

  protected

  def create_customer_form_submission
    FormSubmission.create customer_id: @customer.id,
                          account_id: @account.id,
                          hash_string: @customer.hash_string,
                          page: 'spin_to_win',
                          submitted_on: Date.today,
                          accepted_texting: true,
                          accepted_email: true,
                          new_especial_signup: false,
                          new_texting_signup: false,
                          ip_address: request.remote_ip,
                          device_name: request.user_agent,
                          device_type: request.user_agent
  end

  def load_account
    if Rails.env.development? || Rails.env.test?
      # @account = Account.find_by_domain_name(request.host_with_port)
      # @account ||= Account.find_by_domain_name(
      #   request.host_with_port.gsub(/www./, '').gsub(/^m\./, '')
      # )

      @account = Account.find 11_649
    else
      @account = Account.find_by_domain_name(request.host.gsub(/www./, ''))
      @account ||= Account.where(list_domain_name: request.host).first
    end
  end

  # see also rack-cors config
  def set_cors_headers
    headers['Access-Control-Allow-Origin'] = '*'
    headers['Access-Control-Allow-Methods'] = 'POST, PUT, DELETE, GET, OPTIONS'
    headers['Access-Control-Request-Method'] = '*'
    headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
  end

  def protect_with_recaptcha
    return unless Rails.env.production?

    @account = Account.where(spin_to_win_key: params[:key]).first

    request_domain = request.host.gsub(/^www\./, '').gsub(/^m\./, '').downcase
    account_domain = @account.domain_name.gsub(/:\d+$/, '').downcase
    if @account.list_domain_name.present? &&
       @account.list_domain_name.downcase == request_domain.downcase
      account_domain = request_domain
    end

    if request.xhr?
      uri = URI('https://www.google.com/recaptcha/api/siteverify')
      result = Net::HTTP.post_form(uri, secret: Block::RECAPTCHA_SECRET_KEY,
                                        response: params['g-recaptcha-response'],
                                        remoteip: request.remote_ip)
      Rails.logger.info result.body
      json = JSON.parse(result.body)
      success = json['success']

      if !success
        @error = 'Captcha verification failed.'
        respond_to do |format|
          format.json { render json: { error: @error }, status: 422 }
        end
      end
    else
      unless request.get? || (request_domain == account_domain && verify_recaptcha)
        render text: 'Captcha verification failed. If this was in error, please navigate back and try again.'
      end
    end
  end

  private

  def domain_cache_directory
    Rails.root.join('public', 'cached', request.host)
  end
end
